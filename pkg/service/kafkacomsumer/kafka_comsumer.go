package kafkacomsumer

import (
	"context"
	"encoding/json"

	"git.code.oa.com/trpc-go/trpc-database/kafka"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/IBM/sarama"

	"git.woa.com/RondaServing/ServingControllerProjects/DeploySLADataProcessor/pkg/deploysla"
)

type KafkaConsumer struct {
}

func (KafkaConsumer) Handle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if rawContext, ok := kafka.GetRawSaramaContext(ctx); ok {
		log.Infof("InitialOffset: %d", rawContext.Claim.InitialOffset())
	}
	log.Infof("get kafka message key: %s, value: %s", string(msg.Key), string(msg.Value))
	var data deploysla.DeploySLAData
	if err := json.Unmarshal(msg.Value, &data); err != nil {
		log.Errorf("unmarshal kafka message failed:%s", err.Error())
		return nil
	}
	if err := deploysla.ReplaceDeploySlaDataInDatabase(&data); err != nil {
		log.Errorf("replace deploy sla data in database failed:%s", err.Error())
		return nil
	}

	// 当返回 nil 时，插件才会确认消费成功
	// 当返回非 nil时，插件会休眠 3s 后重新消费。
	// 通常不建议这么你返回 非 nil，由于某些原因，重试之后，还是可能会继续返回 nil 从而导致出现无限重试消费，断重试同一个消息，出现消费卡住的现象。
	// 对于 Handle 失败的情况，自行实现重试逻辑会更稳妥些。
	// 除此之外 Handle 中做耗时非常长的任务，也会导致框架认为消息消费不成功，并一直重试，也会出现消费卡住的现象。
	return nil
}
