package monitor

import (
	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/DeploySLADataProcessor/pkg/monitor/monreporter"
)

// reportCHSize 告警队列长度
const reportCHSize = 1000

// reportCH 告警队列
var reportCH = make(chan reportData, reportCHSize)

// reportData 监控告警数据结构
type reportData struct {
	data interface{}
	try  int
}

// sendDataToReportCH 发送数据到上报监控告警队列中
func sendDataToReportCH(data reportData) {
	select {
	case reportCH <- data:
	default:
		log.Errorf("Can't send to report channel:%+v", data)
	}
}

// ReportCustomWarning 发送告警,自定义告警
func ReportCustomWarning(warn monreporter.CustomWarning) {
	sendDataToReportCH(reportData{data: warn})
}

func startDealWarning() {
	for report := range reportCH {
		err := GetMonitorReporter().MonitorReport(report.data)
		if err != nil {
			log.Errorf("%s", err.Error())
			report.try++
			if report.try >= 2 {
				log.Errorf("Report failed too many times:%+v", report.data)
			} else {
				sendDataToReportCH(report)
			}
		}
	}
}
