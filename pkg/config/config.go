// Package config 实现了配置相关功能,包括从七彩石读取,全局变量,日志控制变量等
package config

import (
	"context"
	"sync/atomic"

	tc "git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"gopkg.in/yaml.v3"
)

const (
	// 主板七彩石
	providerRainbowTconf = "rainbow_tconf"
	// configFileName 配置文件名
	configFileName = "config.yaml"
)

var (
	// gloConf 全局配置,读取rainbow
	gloConf atomic.Value
)

func init() {
	gloConf.Store(&Config{})
}

// Initialize  初始化
func Initialize() {
	log.Info("Init config")
	// 初始化主板七彩石配置
	conf := &Config{}
	err := tc.GetYAML(configFileName, conf)
	if err != nil {
		log.Fatalf("GetYAML error:%s", err.Error())
	}
	// 保存配置
	setConfig(conf)
	// 打印config的yaml
	logConfigWithYaml(conf)
	// 监听配置变更
	watchConfig()
}

// MasterSlaveConfig 服务主备配置
type MasterSlaveConfig struct {
	// IsMasterSlave 是否启用主备
	IsMasterSlave bool `yaml:"isMasterSlave"`
	// LockKey 主备抢锁key, 正式环境：/convert/formal/lock/, 测试环境：/convert/test/lock/
	LockKeyPrefix string `yaml:"lockKeyPrefix"`
	// ETCDUserName etcd用户名
	ETCDUserName string `yaml:"etcdUserName"`
	// ETCDPasswd etcd密码
	ETCDPasswd string `yaml:"etcdPasswd"`
	// ETCDAddr etcd地址
	ETCDAddr string `yaml:"etcdAddr"`
	// KeepAliveInterval 保活超时时间
	KeepAliveIterval int64 `yaml:"keepAliveInterval"`
}

type databaseConfig struct {
	// ----Mysql 数据库连接必须字段
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Dbname   string `yaml:"dbname"`
	Charset  string `yaml:"charset"`
	// ----数据库连接的一些配置
	// MaxOpenConns 最大打开连接数
	MaxOpenConns int `yaml:"maxOpenConns"`
	// MaxIdleCons 最大空闲连接数
	MaxIdleCons int `yaml:"maxIdleCons"`
}

// Config 服务的配置,注意每增加一项要在 setConfig 检查并设置其默认值
type Config struct {
	MasterSlaveConfig MasterSlaveConfig `yaml:"masterSlaveConfig"` // 主备配置
	Database          databaseConfig    `yaml:"database"`          // 数据库配置
}

// watchConfig 监听配置
func watchConfig() {
	rainbowTconfCH := getWatchCH(providerRainbowTconf)
	go func() {
		for {
			select {
			case data, ok := <-rainbowTconfCH:
				if !ok {
					// TODO(benshen):告警
					log.Infof("rainbowMainCH closed")
					break
				}
				if newConf, ok := unmarshalConfig(data); ok {
					setConfig(newConf)
					logConfigWithYaml(newConf)
				}
			}
		}
	}()
}

// unmarshalConfig 解析配置
func unmarshalConfig(data tc.Response) (*Config, bool) {
	if data.Event() != tc.EventTypePut {
		return nil, false
	}
	conf := &Config{}
	log.Infof("Recv config update:%d\n%s", data.Event(), data.Value())
	if err := yaml.Unmarshal([]byte(data.Value()), conf); err != nil {
		log.Errorf("Unmarshal yaml failed:%s", err.Error())
		return nil, false
	}
	return conf, true
}

func getWatchCH(provider string) <-chan tc.Response {
	kvConfig := tc.Get(provider)
	if kvConfig == nil {
		log.Fatalf("trpc config get error, return a null kv config")
	}
	watchCH, err := tc.Get(provider).Watch(context.TODO(), configFileName)
	if err != nil {
		log.Fatalf("Watch config:%s error:%s", configFileName, err.Error())
	}
	return watchCH
}

// GetConfig 获取全局的配置,调用的时候要注意配置会被 Watch,即有可能中途改变,不允许改变的要另外保存
func GetConfig() *Config {
	conf := gloConf.Load().(*Config)
	return conf
}

// setConfig 设置配置变量入口
func setConfig(conf *Config) {
	// 检查参数,设置最小默认值
	checkConfig(conf)
	gloConf.Store(conf)
}

// checkConfig 检查配置合法性
func checkConfig(conf *Config) {
}

func logConfigWithYaml(conf *Config) {
	// 用yaml的格式打印出来
	if yamlData, err := yaml.Marshal(conf); err != nil {
		log.Errorf("Failed to marshal config to YAML: %v", err)
	} else {
		log.Infof("Config after set:\n%s", string(yamlData))
	}
}
