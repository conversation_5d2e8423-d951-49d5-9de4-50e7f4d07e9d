package deploysla

import (
	"time"

	"git.woa.com/RondaServing/ServingControllerProjects/DeploySLADataProcessor/pkg/checkpoint"
)

const tableName = "t_deploy_sla_data"

// DeploySLAData 上线SLA数据
type DeploySLAData struct {
	TaskID                   string    `json:"taskID" db:"task_id"`
	AppGroupID               string    `json:"appGroupID" db:"app_group_id"`
	ServerID                 int       `json:"serverID" db:"server_id"`
	ModelName                string    `json:"modelName" db:"model_name"`
	ModelVersion             string    `json:"modelVersion" db:"model_version"`
	IsSuccess                bool      `json:"isSuccess" db:"is_success"`
	DeployStart              time.Time `json:"deployStart" db:"deploy_start"`
	DeployEnd                time.Time `json:"deployEnd" db:"deploy_end"`
	Duration                 int       `json:"duration" db:"duration"`
	ShouldConvert            bool      `json:"shouldConvert" db:"should_convert"`
	ConvertStart             time.Time `json:"convertStart" db:"convert_start"`
	ConvertEnd               time.Time `json:"convertEnd" db:"convert_end"`
	ConvertDuration          int       `json:"convertDuration" db:"convert_duration"`
	ContainCompleteVersions  int       `json:"containCompleteVersions" db:"contain_complete_versions"`   // 包含多少全量版本(0 or 1)
	ContainIncrementVersions int       `json:"containIncrementVersions" db:"contain_increment_versions"` // 包含多少增量版本(>=0)
}

func ReplaceDeploySlaDataInDatabase(workerInst *DeploySLAData) error {
	_, err := checkpoint.StructReplace(checkpoint.GetDBHandler(), tableName, workerInst)
	return err
}
