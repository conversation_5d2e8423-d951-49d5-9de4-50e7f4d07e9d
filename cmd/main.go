package main

import (
	"context"
	"fmt"

	schttp "git.code.oa.com/RondaServing/ServingController/http"
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	"git.code.oa.com/trpc-go/trpc-database/kafka"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	trpchttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-tjg"
	_ "git.woa.com/galileo/trpc-go-galileo"
	_ "go.uber.org/automaxprocs"

	"git.woa.com/RondaServing/ServingControllerProjects/DeploySLADataProcessor/pkg/checkpoint"
	"git.woa.com/RondaServing/ServingControllerProjects/DeploySLADataProcessor/pkg/config"
	"git.woa.com/RondaServing/ServingControllerProjects/DeploySLADataProcessor/pkg/etcd"
	"git.woa.com/RondaServing/ServingControllerProjects/DeploySLADataProcessor/pkg/monitor"
	"git.woa.com/RondaServing/ServingControllerProjects/DeploySLADataProcessor/pkg/service/kafkacomsumer"
)

// GetETCDLock 取得etcd锁
func GetETCDLock(masterSlaveConfig *config.MasterSlaveConfig) {
	log.Infof("Start get etcd lock ...")
	etcd, err := etcd.NewEtcd(masterSlaveConfig.ETCDUserName, masterSlaveConfig.ETCDPasswd,
		masterSlaveConfig.ETCDAddr, masterSlaveConfig.LockKeyPrefix, 10)
	if err != nil {
		log.Fatalf("Init etcd err %v", err)
		return
	}
	if err := etcd.Campaign(context.Background()); err != nil {
		log.Fatalf("Etcd campaign failed, err=%s", err.Error())
	}
	log.Infof("Get etcd lock success ...")
	return
}

func main() {
	// NewServer() 调用后才会初始化 trpc-go 日志
	tsrv := trpc.NewServer()
	// 初始化配置和全局公共变量
	config.Initialize()
	serverConfig := config.GetConfig()
	// etcd 抢锁
	if serverConfig.MasterSlaveConfig.IsMasterSlave {
		GetETCDLock(&serverConfig.MasterSlaveConfig)
	}
	// 初始化进程其他资源
	checkpoint.Initialize()
	monitor.Initialize()
	// kafka comsumer初始化
	kafka.RegisterKafkaConsumerService(tsrv.Service("trpc.deploysla.consumer"), &kafkacomsumer.KafkaConsumer{})
	// 注册一个http service是为了页面显示unhealthy
	muxRouter := schttp.HandleRegisterdFunc()
	trpchttp.RegisterNoProtocolServiceMux(tsrv.Service(fmt.Sprintf("trpc.%s.%s.Http",
		trpc.GlobalConfig().Server.App,
		trpc.GlobalConfig().Server.Server)),
		muxRouter)
	// 启动trpc服务
	if err := tsrv.Serve(); err != nil {
		log.Error(err.Error())
	}
	log.Info("====> ending")
}
